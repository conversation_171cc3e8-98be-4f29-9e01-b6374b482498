<app-main-layout>
  <div class="container mx-auto px-4 py-6">
    
    <form [formGroup]="enquiryForm" (ngSubmit)="submitEnquiry()" class="bg-white p-6 rounded-lg shadow-md">
      <!-- Tabs -->
      <div class="mb-6 border-b border-gray-200">
        <nav class="flex space-x-4" aria-label="Tabs">
          <button type="button" class="py-2 px-4 text-sm font-medium"
            [ngClass]="{ 'border-b-2 border-teal-600 text-teal-600': activeTab === 'student', 'text-gray-500': activeTab !== 'student' }"
            (click)="onTabClick('student')">Student Details</button>
          <button type="button" class="py-2 px-4 text-sm font-medium"
            [ngClass]="{ 'border-b-2 border-teal-600 text-teal-600': activeTab === 'parent', 'text-gray-500': activeTab !== 'parent' }"
            (click)="onTabClick('parent')">Parent Details</button>
          <button type="button" class="py-2 px-4 text-sm font-medium"
            [ngClass]="{ 'border-b-2 border-teal-600 text-teal-600': activeTab === 'guardian', 'text-gray-500': activeTab !== 'guardian' }"
            (click)="onTabClick('guardian')">Guardian Details</button>
          <button type="button" class="py-2 px-4 text-sm font-medium"
            [ngClass]="{ 'border-b-2 border-teal-600 text-teal-600': activeTab === 'other', 'text-gray-500': activeTab !== 'other' }"
            (click)="onTabClick('other')">Other Info</button>
        </nav>
      </div>

      <!-- Student Tab -->
      <div *ngIf="activeTab === 'student'" formGroupName="student">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <div>
            <label for="studentName" class="block text-sm font-medium text-gray-700 mb-1">Student Name</label>
            <input type="text" id="studentName" formControlName="student_name"
              placeholder="Enter Student Name"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': studentForm.get('student_name')?.invalid && (studentForm.get('student_name')?.touched || submitted)}">
            <p *ngIf="studentForm.get('student_name')?.invalid && (studentForm.get('student_name')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Student name is required</p>
          </div>
          
          <div>
            <label for="dob" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
            <div class="relative">
              <input type="text"
                #dobInput
                readonly
                autocomplete="off"
                class="datepicker w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 pr-10 bg-white"
                formControlName="student_date_of_birth"
                placeholder="Select Date of Birth"
                [ngClass]="{'border-red-500': studentForm.get('student_date_of_birth')?.invalid && (studentForm.get('student_date_of_birth')?.touched || submitted)}"
              />
              <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            <p *ngIf="studentForm.get('student_date_of_birth')?.invalid && (studentForm.get('student_date_of_birth')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Date of birth is required</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
            <div class="flex space-x-4">
              <button type="button" 
                class="px-4 py-2 rounded-md text-sm font-medium focus:outline-none"
                [ngClass]="{'bg-teal-600 text-white': selectedGender === 'male', 'bg-gray-200 text-gray-700': selectedGender !== 'male'}"
                (click)="selectGender('male')">
                Male
              </button>
              <button type="button" 
                class="px-4 py-2 rounded-md text-sm font-medium focus:outline-none"
                [ngClass]="{'bg-teal-600 text-white': selectedGender === 'female', 'bg-gray-200 text-gray-700': selectedGender !== 'female'}"
                (click)="selectGender('female')">
                Female
              </button>
            </div>
            <p *ngIf="studentForm.get('student_gender')?.invalid && (studentForm.get('student_gender')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Gender is required</p>
          </div>
          
          <div>
            <label for="currentGrade" class="block text-sm font-medium text-gray-700 mb-1">Current Grade</label>
            <input type="text" id="currentGrade" formControlName="student_current_grade"
              placeholder="Enter Current Grade"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': studentForm.get('student_current_grade')?.invalid && (studentForm.get('student_current_grade')?.touched || submitted)}">
            <p *ngIf="studentForm.get('student_current_grade')?.invalid && (studentForm.get('student_current_grade')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Current grade is required</p>
          </div>
          
          <div>
            <label for="interestedGrade" class="block text-sm font-medium text-gray-700 mb-1">Interested Grade / Class Enquiry</label>
            <select id="interestedGrade" formControlName="class_id"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': studentForm.get('class_id')?.invalid && (studentForm.get('class_id')?.touched || submitted)}">
              <option value="" disabled selected>Select Interested Grade / Class Enquiry</option>
              @for (item of classListData; track item.id) {
                <option [value]="item.id">{{ item.name }}</option>
              }
            </select>
            <p *ngIf="studentForm.get('class_id')?.invalid && (studentForm.get('class_id')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Interested grade is required</p>
          </div>
          
          <div>
            <label for="previousSchool" class="block text-sm font-medium text-gray-700 mb-1">Previous School (if any)</label>
            <input type="text" id="previousSchool" formControlName="student_previous_school"
              placeholder="Enter Previous School (if any)"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
          </div>
          
          <div>
            <label for="aadhaar" class="block text-sm font-medium text-gray-700 mb-1">Aadhaar Number</label>
            <input type="text" id="aadhaar" formControlName="student_aadhaar_number"
              placeholder="Enter Aadhaar Number"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': studentForm.get('student_aadhaar_number')?.invalid && (studentForm.get('student_aadhaar_number')?.touched || submitted)}">
            <p *ngIf="studentForm.get('student_aadhaar_number')?.invalid && (studentForm.get('student_aadhaar_number')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">
              <span *ngIf="studentForm.get('aadhaar')?.errors?.['required']">Aadhaar number is required</span>
              <span *ngIf="studentForm.get('aadhaar')?.errors?.['pattern']">Aadhaar must be 12 digits</span>
            </p>
          </div>
        </div>
        
        <div class="md:col-span-2 flex justify-end">
          <button type="button" (click)="nextTab('parent')" 
            class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg"
            [disabled]="studentForm.invalid">
            Next
          </button>
        </div>
      </div>

      <!-- Parent Tab -->
      <div *ngIf="activeTab === 'parent'" formGroupName="parent">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <div>
            <label for="fatherName" class="block text-sm font-medium text-gray-700 mb-1">Father's Name</label>
            <input type="text" id="fatherName" formControlName="father_name"
              placeholder="Enter Father's Name"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': parentForm.get('father_name')?.invalid && (parentForm.get('father_name')?.touched || submitted)}">
            <p *ngIf="parentForm.get('father_name')?.invalid && (parentForm.get('father_name')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Father's name is required</p>
          </div>

          <div>
            <label for="motherName" class="block text-sm font-medium text-gray-700 mb-1">Mother's Name</label>
            <input type="text" id="motherName" formControlName="mother_name"
              placeholder="Enter Mother's Name"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': parentForm.get('mother_name')?.invalid && (parentForm.get('mother_name')?.touched || submitted)}">
            <p *ngIf="parentForm.get('mother_name')?.invalid && (parentForm.get('mother_name')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Mother's name is required</p>
          </div>
          
          <div>
            <label for="fatherOccupation" class="block text-sm font-medium text-gray-700 mb-1">Father's Occupation</label>
            <input type="text" id="fatherOccupation" formControlName="father_occupation"
              placeholder="Enter Father's Occupation"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
            <p *ngIf="parentForm.get('father_occupation')?.invalid && (parentForm.get('father_occupation')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Father's occupation is required</p>
          </div>

          <div>
            <label for="motherOccupation" class="block text-sm font-medium text-gray-700 mb-1">Mother's Occupation</label>
            <input type="text" id="motherOccupation" formControlName="mother_occupation"
              placeholder="Enter Mother's Occupation"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
            <p *ngIf="parentForm.get('mother_occupation')?.invalid && (parentForm.get('mother_occupation')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">Mother's occupation is required</p>
          </div>
          
          <div>
            <label for="fatherPhone" class="block text-sm font-medium text-gray-700 mb-1">Father's Phone Number</label>
            <input type="text" id="fatherPhone" formControlName="father_phone_number"
              placeholder="Enter Father's Phone Number"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': parentForm.get('father_phone_number')?.invalid && (parentForm.get('father_phone_number')?.touched || submitted)}">
            <p *ngIf="parentForm.get('father_phone_number')?.invalid && (parentForm.get('father_phone_number')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">
              <span *ngIf="parentForm.get('father_phone_number')?.errors?.['required']">Father's phone is required</span>
              <span *ngIf="parentForm.get('father_phone_number')?.errors?.['pattern']">Phone must be 10 digits</span>
            </p>
          </div>
          
          <div>
            <label for="motherPhone" class="block text-sm font-medium text-gray-700 mb-1">Mother's Phone Number</label>
            <input type="text" id="motherPhone" formControlName="mother_phone_number"
              placeholder="Enter Mother's Phone Number"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': parentForm.get('mother_phone_number')?.invalid && (parentForm.get('mother_phone_number')?.touched || submitted)}">
            <p *ngIf="parentForm.get('mother_phone_number')?.invalid && (parentForm.get('mother_phone_number')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">
              <span *ngIf="parentForm.get('mother_phone_number')?.errors?.['required']">Mother's phone is required</span>
              <span *ngIf="parentForm.get('mother_phone_number')?.errors?.['pattern']">Phone must be 10 digits</span>
            </p>
          </div>
        </div>
        
        <div class="md:col-span-2 flex justify-between pt-2.5">
          <button type="button" (click)="prevTab()" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
            Previous
          </button>
          <button type="button" (click)="nextTab()" 
            class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg"
            [disabled]="parentForm.invalid">
            Next
          </button>
        </div>
      </div>

      <!-- Guardian Tab -->
      <div *ngIf="activeTab === 'guardian'" formGroupName="guardian" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="guardianName" class="block text-sm font-medium text-gray-700">Guardian’s Name</label>
          <input type="text" id="guardianName" formControlName="guardian_name" placeholder="Enter Guardian's Name" class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
        </div>
        <div>
          <label for="guardianOccupation" class="block text-sm font-medium text-gray-700">Guardian’s Occupation</label>
          <input type="text" id="guardianOccupation" formControlName="guardian_occupation" placeholder="Enter Guardian's Occupation" class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
        </div>

        <div>
            <label for="guardianPhone" class="block text-sm font-medium text-gray-700 mb-1">Guardian's Phone Number</label>
            <input type="text" id="guardianPhone" formControlName="guardian_phone_number"
              placeholder="Enter Guardian's Phone Number"
              class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
              [ngClass]="{'border-red-500': guardianForm.get('guardian_phone_number')?.invalid && (guardianForm.get('guardian_phone_number')?.touched || submitted)}">
            <p *ngIf="guardianForm.get('guardian_phone_number')?.invalid && (guardianForm.get('guardian_phone_number')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">
              <span *ngIf="guardianForm.get('guardian_phone_number')?.errors?.['required']">Guardian's phone is required</span>
              <span *ngIf="guardianForm.get('guardian_phone_number')?.errors?.['pattern']">Phone must be 10 digits</span>
            </p>
          </div>
        <div class="md:col-span-2 flex justify-between">
          <button type="button" (click)="prevTab()" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
            Previous
          </button>
          <button type="button" (click)="nextTab()" 
            class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg"
            [disabled]="!guardianForm.valid">
            Next
          </button>
        </div>
      </div>

      <!-- Other Info Tab -->
      <div *ngIf="activeTab === 'other'" formGroupName="other" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="address1" class="block text-sm font-medium text-gray-700">Address 1</label>
          <input type="text" id="address1" formControlName="address_1" placeholder="Enter Address 1"
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
        </div>
        <div>
          <label for="address2" class="block text-sm font-medium text-gray-700">Address 2</label>
          <input type="text" id="address2" formControlName="address_2" placeholder="Enter Address 2"
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
        </div>
        <div>
          <label for="landmark" class="block text-sm font-medium text-gray-700">Landmark</label>
          <input type="text" id="landmark" formControlName="landmark" placeholder="Enter Landmark"
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
        </div>
        <div>
          <label for="city" class="block text-sm font-medium text-gray-700">City</label>
          <input type="text" id="city" formControlName="city" placeholder="Enter City"
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
            <p *ngIf="otherForm.get('city')?.invalid && (otherForm.get('city')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">City is required</p>
        </div>
        <div>
          <label for="state" class="block text-sm font-medium text-gray-700">State</label>
          <input type="text" id="state" formControlName="state" placeholder="Enter State"
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
            <p *ngIf="otherForm.get('state')?.invalid && (otherForm.get('state')?.touched || submitted)" 
               class="mt-1 text-xs text-red-600">State is required</p>
        </div>
        <div>
          <label for="pincode" class="block text-sm font-medium text-gray-700">Pincode</label>
          <input type="text" id="pincode" formControlName="pincode" placeholder="Enter Pincode"
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
          <p *ngIf="otherForm.get('pincode')?.invalid && (otherForm.get('pincode')?.touched || submitted)" 
              class="mt-1 text-xs text-red-600">
            <span *ngIf="otherForm.get('pincode')?.errors?.['required']">Pincode is required</span>
            <span *ngIf="otherForm.get('pincode')?.errors?.['pattern']">Pincode must be 6 digits</span>
          </p>
        </div>
        <div class="md:col-span-2 flex justify-between">
          <button type="button" (click)="prevTab()" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
            Previous
          </button>
          <button type="submit" 
            class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg"
            [disabled]="!otherForm.valid">
            Submit Enquiry
          </button>
        </div>
      </div>
    </form>
  </div>
</app-main-layout>
