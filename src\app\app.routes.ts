import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';

export const routes: Routes = [
    {
        path: "",
        redirectTo: 'login',
        pathMatch: 'full'
    },
    {
        path: "login",
        loadComponent: () => import('./components/login/login.component').then(c => c.LoginComponent),
    },
    {
        path: "dashboard",
        canActivate: [authGuard],
        loadComponent: () => import('./components/dashboard/dashboard.component').then(c => c.DashboardComponent),
    },
    {
        path: "attendance",
        canActivate: [authGuard],
        loadComponent: () => import('./components/dashboard/dashboard.component').then(c => c.DashboardComponent),
    },
    {
        path: "enquiry",
        canActivate: [authGuard],
        loadComponent: () => import('./components/enquiry/enquiry.component').then(c => c.EnquiryComponent),
    },
    {
        path: "enquiry/create",
        canActivate: [authGuard],
        loadComponent: () => import('./components/enquiry/enquiry-create/enquiry-create.component').then(c => c.EnquiryCreateComponent),
    },
    {
        path: "enquiry/:categorySlug",
        canActivate: [authGuard],
        loadComponent: () => import('./components/enquiry/enquiry-list/enquiry-list.component').then(c => c.EnquiryListComponent),
    },
    {
        path: "enquiry/create/:categorySlug",
        canActivate: [authGuard],
        loadComponent: () => import('./components/enquiry/enquiry-create/enquiry-create.component').then(c => c.EnquiryCreateComponent),
    },
    {
        path: "enquiry/edit/:id/:categorySlug",
        canActivate: [authGuard],
        loadComponent: () => import('./components/enquiry/enquiry-create/enquiry-create.component').then(c => c.EnquiryCreateComponent),
    },
    {
        path: "admission",
        canActivate: [authGuard],
        loadComponent: () => import('./components/dashboard/dashboard.component').then(c => c.DashboardComponent),
    },
    {
        path: "payments",
        canActivate: [authGuard],
        loadComponent: () => import('./components/dashboard/dashboard.component').then(c => c.DashboardComponent),
    },
    {
        path: "users",
        canActivate: [authGuard],
        loadComponent: () => import('./components/dashboard/dashboard.component').then(c => c.DashboardComponent),
    },
    {
        path: "**",
        redirectTo: 'login'
    }
];
