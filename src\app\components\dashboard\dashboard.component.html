<app-main-layout>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Loading State -->
    <div *ngIf="loading" class="col-span-3 flex justify-center items-center py-10">
      <svg class="animate-spin h-10 w-10 text-teal-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>

    <!-- Error State -->
    <div *ngIf="!stats && !loading" class="col-span-3 bg-red-100 border border-red-200 text-red-700 px-4 py-3 rounded">
      <p>Failed to load dashboard data. Please try again later.</p>
      <button (click)="fetchDashboardStats()" class="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
        Retry
      </button>
    </div>

    <!-- Students Card -->
    <div *ngIf="stats && !loading" class="bg-white rounded-lg shadow p-6 flex items-center">
      <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      </div>
      <div>
        <h3 class="text-green-600 font-medium">Students</h3>
        <p class="text-gray-500 text-sm">Total Users</p>
        <p class="text-2xl font-bold">{{ stats.students.capacity }}</p>
      </div>
    </div>

    <!-- Teachers Card -->
    <div *ngIf="stats && !loading" class="bg-white rounded-lg shadow p-6 flex items-center">
      <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <div>
        <h3 class="text-blue-600 font-medium">Teachers</h3>
        <p class="text-gray-500 text-sm">Total Users</p>
        <p class="text-2xl font-bold">{{ stats.teachers.total }}/{{ stats.teachers.capacity }}</p>
      </div>
    </div>

    <!-- Staff Card -->
    <div *ngIf="stats && !loading" class="bg-white rounded-lg shadow p-6 flex items-center">
      <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      </div>
      <div>
        <h3 class="text-purple-600 font-medium">Staff</h3>
        <p class="text-gray-500 text-sm">Total Users</p>
        <p class="text-2xl font-bold">{{ stats.staff.total }}/{{ stats.staff.capacity }}</p>
      </div>
    </div>
  </div>

  <!-- Action Button -->
  <div class="mt-6">
    <a routerLink="/attendance" class="block w-full bg-teal-800 hover:bg-teal-900 text-white text-center py-3 rounded-md transition">
      Take Attendance
    </a>
  </div>
</app-main-layout>
