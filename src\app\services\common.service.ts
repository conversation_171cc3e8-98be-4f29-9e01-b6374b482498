import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { ApiResponse, Branch, DashboardStats, LoginCredentials, User, EnquiryData, EnquiryList, ClassList } from '../models/interface/allinterfaces';

@Injectable({
  providedIn: 'root'
})
export class CommonService {
  private baseURL = environment.API_URL;
  constructor(private http: HttpClient) { }

  // Remove the static token and headers properties
  // Instead, create a method to get the current headers
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('token') || '';
    return new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    });
  }

  getBranchList(): Observable<ApiResponse<Branch[]>> {
    return this.http.get<ApiResponse<Branch[]>>(`${this.baseURL}branch-list`);
  }
  
  getLogin(credentials: LoginCredentials): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseURL}user/login`, credentials);
  }

  getDashboardStats(): Observable<ApiResponse<DashboardStats>> {
    return this.http.get<ApiResponse<DashboardStats>>(
      `${this.baseURL}dashboard/stats`, 
      { headers: this.getHeaders() }
    );
  }

  getCurrentUser(): Observable<ApiResponse<User>> {
    return this.http.get<ApiResponse<User>>(
      `${this.baseURL}user/profile`,
      { headers: this.getHeaders() }
    );
  }

  logout(): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(
      `${this.baseURL}user/logout`, 
      {}, 
      { headers: this.getHeaders() }
    );
  }

  getEnquiryData(): Observable<ApiResponse<EnquiryData>> {
    return this.http.get<ApiResponse<EnquiryData>>(
      `${this.baseURL}enquiry`,
      { headers: this.getHeaders() }
    );
  }

  getEnquiryList(type: string): Observable<ApiResponse<EnquiryList>> {
    return this.http.get<ApiResponse<EnquiryList>>(
      `${this.baseURL}enquiry/${type}`,
      { headers: this.getHeaders() }
    );
  }

  getClassList(): Observable<ApiResponse<ClassList>> {
    return this.http.get<ApiResponse<ClassList>>(
      `${this.baseURL}class-list`,
      { headers: this.getHeaders() }
    );
  }

  storeEnquiry(enquiryData: any): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(
      `${this.baseURL}enquiry/store`,
      enquiryData,
      { headers: this.getHeaders() }
    );
  }

  getEnquiryById(enquiry: { id: string | number, class: string }): Observable<ApiResponse<any>> {
    return this.http.post<ApiResponse<any>>(`${this.baseURL}enquiry/edit`, enquiry, { headers: this.getHeaders() });
  }

  deleteEnquiry(enquiryId: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(
      `${this.baseURL}enquiry/${enquiryId}`,
      { headers: this.getHeaders() }
    );
  }
}
