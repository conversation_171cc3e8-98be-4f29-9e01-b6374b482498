import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { CommonService } from '../../services/common.service';
import { AcademicYear, EnquiryCategory, EnquiryData } from '../../models/interface/allinterfaces';
import { SharedModule } from '../../shared/shared.module';

@Component({
  selector: 'app-enquiry',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink, SharedModule],
  templateUrl: './enquiry.component.html',
  styleUrl: './enquiry.component.css'
})
export class EnquiryComponent implements OnInit {
  loading = true;
  error = false;
  academicYears: AcademicYear[] = [];
  categories: EnquiryCategory[] = [];
  selectedYear: string = '';

  constructor(private commonService: CommonService) {}

  ngOnInit(): void {
    this.fetchEnquiryData();
  }

  fetchEnquiryData(): void {
    this.loading = true;
    this.commonService.getEnquiryData().subscribe({
      next: (response) => {
        console.log('Enquiry Data Response:', response);
        console.log('Academic Years:', response.data.academicYears);
        console.log('enquiryData:', response.data.enquiryData);
        if (response.status) {
          this.academicYears = response.data.academicYears;
          this.categories = response.data.enquiryData;
          
          // Set default selected year to '2024-25' if available
          const defaultYear = this.academicYears.find(year => year.name === '2024-25');
          if (defaultYear) {
            this.selectedYear = defaultYear.id;
          } else if (this.academicYears.length > 0) {
            this.selectedYear = this.academicYears[0].id;
          }
        } else {
          this.error = true;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching enquiry data:', err);
        this.error = true;
        this.loading = false;
      }
    });
  }

  onYearChange(): void {
    // Reload data based on selected year
    // This would typically make another API call with the selected year as a parameter
    // For now, we'll just simulate this by re-fetching the data
    this.fetchEnquiryData();
  }

  addNewEnquiry(): void {
    // Navigate to add new enquiry form
    // This would be implemented in a future task
  }
}
