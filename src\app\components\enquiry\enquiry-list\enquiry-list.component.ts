import { Component, OnInit } from '@angular/core';
import { SharedModule } from '../../../shared/shared.module';
import { CommonService } from '../../../services/common.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';

@Component({
  selector: 'app-enquiry-list',
  standalone: true,
  imports: [CommonModule, FormsModule, SharedModule, RouterModule],
  templateUrl: './enquiry-list.component.html',
  styleUrl: './enquiry-list.component.css'
})
export class EnquiryListComponent implements OnInit {
  loading = true;
  error = false;
  searchText: string = '';
  enquiryListData: any[] = []; // Adjust type as needed
  categorySlug: string = ''; // Add a property to store the category slug from the route

  constructor(private commonService: CommonService, private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.categorySlug = this.route.snapshot.params['categorySlug']; // Get the category slug from the route
    this.fetchEnquiryListData();
  }

  fetchEnquiryListData(): void {
    this.loading = true;
    this.commonService.getEnquiryList(this.categorySlug).subscribe({
      next: (response) => {
        console.log('Enquiry Data Response:', response);
        console.log('enquiryListData:', response.data.enquiryListData);
        if (response.status) {
          // Handle successful response
          this.enquiryListData = response.data.enquiryListData;
        } else {
          this.error = true;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching enquiry data:', err);
        this.error = true;
        this.loading = false;
      }
    });
  }

  deleteEnquiry(enquiryId: number): void {
    this.commonService.deleteEnquiry(enquiryId).subscribe({
      next: (response) => {
        if (response.status) {
          // Remove the deleted enquiry from the list
          this.enquiryListData = this.enquiryListData.filter(enquiry => enquiry.id !== enquiryId);
        } else {
          this.error = true;
        }
      },
      error: (err) => {
        console.error('Error deleting enquiry:', err);
        this.error = true;
      }
    });
  }
}
